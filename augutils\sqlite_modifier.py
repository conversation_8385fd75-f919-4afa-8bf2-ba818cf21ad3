import sqlite3
import shutil
import time
import os
from utils.paths import get_db_path

def _create_backup(file_path: str) -> str:
    """
    Creates a backup of the specified file with timestamp.
    
    Args:
        file_path (str): Path to the file to backup
        
    Returns:
        str: Path to the backup file
        
    Format: <filename>.bak.<timestamp>
    """
    timestamp = int(time.time())
    backup_path = f"{file_path}.bak.{timestamp}"
    shutil.copy2(file_path, backup_path)
    return backup_path

def clean_augment_data(appname="Code") -> dict:
    """
    Cleans augment-related data from the SQLite database.
    Creates a backup before modification.
    
    Args:
        appname (str): Application name, either "Code" for VS Code or "Cursor" for Cursor
    
    This function:
    1. Gets the SQLite database path
    2. Creates a backup of the database file
    3. Opens the database connection
    4. Deletes records where key contains 'augment'
    
    Returns:
        dict: A dictionary containing operation results
        {
            'db_backup_path': str,
            'deleted_rows': int
        }
    """
    db_path = get_db_path(appname)
    
    # 确保数据库文件目录存在
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    # 初始化变量
    db_backup_path = None
    deleted_rows = 0
    
    # 检查数据库文件是否存在
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return {
            'db_backup_path': None,
            'deleted_rows': 0
        }
    
    # 创建备份
    db_backup_path = _create_backup(db_path)
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ItemTable'")
            if cursor.fetchone() is None:
                print("ItemTable表不存在")
                return {
                    'db_backup_path': db_backup_path,
                    'deleted_rows': 0
                }
            
            # 执行删除查询
            cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
            deleted_rows = cursor.rowcount
            
            # 提交更改
            conn.commit()
            
            return {
                'db_backup_path': db_backup_path,
                'deleted_rows': deleted_rows
            }
        finally:
            # 始终关闭连接
            cursor.close()
            conn.close()
    except sqlite3.Error as e:
        print(f"SQLite错误: {e}")
        return {
            'db_backup_path': db_backup_path,
            'deleted_rows': 0
        } 