import os
import sys
from pathlib import Path


def get_home_dir() -> str:
    """
    Get the user's home directory across different platforms.
    
    Returns:
        str: Path to the user's home directory
    """
    return str(Path.home())


def get_app_data_dir() -> str:
    """
    Get the application data directory across different platforms.
    
    Returns:
        str: Path to the application data directory
        
    Platform specific paths:
        - Windows: %APPDATA% (typically C:\\Users\\<USER>\\AppData\\Roaming)
        - macOS: ~/Library/Application Support
        - Linux: ~/.local/share
    """
    if sys.platform == "win32":
        # Windows
        return os.getenv("APPDATA", "")
    elif sys.platform == "darwin":
        # macOS
        return os.path.join(str(Path.home()), "Library/Application Support")
    else:
        # Linux and other Unix-like systems
        return os.path.join(str(Path.home()), ".local/share")


def get_storage_path(appname="Code") -> str:
    """
    Get the storage.json path across different platforms.
    
    Args:
        appname (str): Application name, either "Code" for VS Code or "Cursor" for Cursor
        
    Returns:
        str: Path to the storage.json file
        
    Platform specific paths:
        - Windows: %APPDATA%/[appname]/User/globalStorage/storage.json
        - macOS: ~/Library/Application Support/[appname]/User/globalStorage/storage.json
        - Linux: ~/.config/[appname]/User/globalStorage/storage.json
    """
    if sys.platform == "win32":
        # Windows
        base_path = os.getenv("APPDATA", "")
        return os.path.join(base_path, appname, "User", "globalStorage", "storage.json")
    elif sys.platform == "darwin":
        # macOS
        return os.path.join(str(Path.home()), "Library", "Application Support", appname, "User", "globalStorage", "storage.json")
    else:
        # Linux and other Unix-like systems
        return os.path.join(str(Path.home()), ".config", appname, "User", "globalStorage", "storage.json")


def get_db_path(appname="Code") -> str:
    """
    Get the state.vscdb path across different platforms.
    
    Args:
        appname (str): Application name, either "Code" for VS Code or "Cursor" for Cursor
        
    Returns:
        str: Path to the state.vscdb file
        
    Platform specific paths:
        - Windows: %APPDATA%/[appname]/User/globalStorage/state.vscdb
        - macOS: ~/Library/Application Support/[appname]/User/globalStorage/state.vscdb
        - Linux: ~/.config/[appname]/User/globalStorage/state.vscdb
    """
    if sys.platform == "win32":
        # Windows
        base_path = os.getenv("APPDATA", "")
        return os.path.join(base_path, appname, "User", "globalStorage", "state.vscdb")
    elif sys.platform == "darwin":
        # macOS
        return os.path.join(str(Path.home()), "Library", "Application Support", appname, "User", "globalStorage", "state.vscdb")
    else:
        # Linux and other Unix-like systems
        return os.path.join(str(Path.home()), ".config", appname, "User", "globalStorage", "state.vscdb")


def get_machine_id_path(appname="Code") -> str:
    """
    Get the machine ID file path across different platforms.
    
    Args:
        appname (str): Application name, either "Code" for VS Code or "Cursor" for Cursor
        
    Returns:
        str: Path to the machine ID file
        
    Platform specific paths:
        - Windows: %APPDATA%/[appname]/User/machineid
        - macOS: ~/Library/Application Support/[appname]/machineid
        - Linux: ~/.config/[appname]/User/machineid
    """
    if sys.platform == "win32":
        # Windows
        base_path = os.getenv("APPDATA", "")
        return os.path.join(base_path, appname, "User", "machineid")
    elif sys.platform == "darwin":
        # macOS
        return os.path.join(str(Path.home()), "Library", "Application Support", appname, "machineid")
    else:
        # Linux and other Unix-like systems
        return os.path.join(str(Path.home()), ".config", appname, "machineid")


def get_workspace_storage_path(appname="Code") -> str:
    """
    Get the workspaceStorage path across different platforms.
    
    Args:
        appname (str): Application name, either "Code" for VS Code or "Cursor" for Cursor
        
    Returns:
        str: Path to the workspaceStorage directory
        
    Platform specific paths:
        - Windows: %APPDATA%/[appname]/User/workspaceStorage
        - macOS: ~/Library/Application Support/[appname]/User/workspaceStorage
        - Linux: ~/.config/[appname]/User/workspaceStorage
    """
    if sys.platform == "win32":
        # Windows
        base_path = os.getenv("APPDATA", "")
        return os.path.join(base_path, appname, "User", "workspaceStorage")
    elif sys.platform == "darwin":
        # macOS
        return os.path.join(str(Path.home()), "Library", "Application Support", appname, "User", "workspaceStorage")
    else:
        # Linux and other Unix-like systems
        return os.path.join(str(Path.home()), ".config", appname, "User", "workspaceStorage") 