from utils.paths import get_home_dir, get_app_data_dir, get_storage_path, get_db_path, get_machine_id_path,get_workspace_storage_path
from augutils.json_modifier import modify_telemetry_ids
from augutils.sqlite_modifier import clean_augment_data
from augutils.workspace_cleaner import clean_workspace_storage
import argparse
import sys

if __name__ == "__main__":
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='Free AugmentCode - 清理AugmentCode相关数据的工具')
    parser.add_argument('--appname', type=str, default='Cursor', 
                        help='应用名称，可选值: "Code"(VS Code) 或 "Cursor"(Cursor编辑器)，默认为"Cursor"')
    args = parser.parse_args()
    
    appname = args.appname
    
    print("System Paths:")
    print(f"Home Directory: {get_home_dir()}")
    print(f"App Data Directory: {get_app_data_dir()}")
    print(f"Storage Path: {get_storage_path(appname)}")
    print(f"DB Path: {get_db_path(appname)}")
    print(f"Machine ID Path: {get_machine_id_path(appname)}")
    print(f"Workspace Storage Path: {get_workspace_storage_path(appname)}")
    
    print(f"\n重要提示: 请确保 {appname} 已完全关闭，否则可能会出现权限错误!")
    
    try:
        print(f"\nModifying Telemetry IDs for {appname}:")
        try:
            result = modify_telemetry_ids(appname)
            if result['storage_backup_path']:
                print("\nBackup created at:")
                print(f"Storage backup path: {result['storage_backup_path']}")
            if result['machine_id_backup_path']:
                print(f"Machine ID backup path: {result['machine_id_backup_path']}")
            
            print("\nOld IDs:")
            print(f"Machine ID: {result['old_machine_id']}")
            print(f"Device ID: {result['old_device_id']}")
            
            print("\nNew IDs:")
            print(f"Machine ID: {result['new_machine_id']}")
            print(f"Device ID: {result['new_device_id']}")
        except PermissionError as e:
            print(f"错误: 无法修改文件，请确保 {appname} 已完全关闭。")
            print(f"详细错误: {e}")
            sys.exit(1)
        except Exception as e:
            print(f"错误: {e}")
            sys.exit(1)
        
        try:
            print(f"\nCleaning SQLite Database for {appname}:")
            db_result = clean_augment_data(appname)
            if db_result['db_backup_path']:
                print(f"Database backup created at: {db_result['db_backup_path']}")
            print(f"Deleted {db_result['deleted_rows']} rows containing 'augment' in their keys")
        except PermissionError as e:
            print(f"错误: 无法修改数据库，请确保 {appname} 已完全关闭。")
            print(f"详细错误: {e}")
        except Exception as e:
            print(f"错误: {e}")
        
        try:
            print(f"\nCleaning Workspace Storage for {appname}:")
            ws_result = clean_workspace_storage(appname)
            if ws_result['backup_path']:
                print(f"Workspace backup created at: {ws_result['backup_path']}")
            print(f"Deleted {ws_result['deleted_files_count']} files from workspace storage")
        except PermissionError as e:
            print(f"错误: 无法清理工作区存储，请确保 {appname} 已完全关闭。")
            print(f"详细错误: {e}")
        except Exception as e:
            print(f"错误: {e}")
        
        print(f"\n现在您可以运行 {appname} 并使用新邮箱登录。")
    except KeyboardInterrupt:
        print("\n操作已取消。")
        sys.exit(1)
    except Exception as e:
        print(f"发生未知错误: {e}")
        sys.exit(1)